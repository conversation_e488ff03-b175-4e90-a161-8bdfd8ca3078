<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Select, SelectOption } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { listUser } from '#/api/waterfee/user/archivesManage';
import {
  addConsolidatedAccount,
  getConsolidatedAccount,
  updateConsolidatedAccount,
} from '#/api/waterfee/user/consolidatedAccount/index';

import { modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      class: 'w-full',
    },
  },
  schema: modalSchema(),
  showDefaultActions: false,
});

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[800px]',
  fullscreenButton: true,
  closeOnClickModal: false,
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);
    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;
    if (isUpdate.value && id) {
      const record = await getConsolidatedAccount(id);
      await formApi.setValues(record);
    }
    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate.value
      ? updateConsolidatedAccount(data)
      : addConsolidatedAccount(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}

const userOptions = ref([]);
const userLoading = ref(false);

async function handleDropdownVisibleChange(open) {
  if (open && userOptions.value.length === 0) {
    userLoading.value = true;
    try {
      const res = await listUser({
        auditStatus: 'finish',
      });
      if (res && res.rows) {
        userOptions.value = res.rows;
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      userLoading.value = false;
    }
  }
}
</script>

<template>
  <BasicModal :close-on-click-modal="false" :title="title">
    <BasicForm>
      <template #userList="slotProps">
        <Select
          v-model:value="slotProps.formModel.userList"
          mode="multiple"
          placeholder="请选择"
          :loading="userLoading"
          @dropdown-visible-change="handleDropdownVisibleChange"
        >
          <template v-for="item in userOptions" :key="item.userNo">
            <SelectOption :value="item.userNo">
              <div class="flex flex-col">
                <span class="font-medium">{{ item.userName || '-' }}</span>
                <span class="font-medium">用户编号：{{ item.userNo }}</span>
                <span class="text-xs text-gray-500">{{
                  item.address || '暂无地址'
                }}</span>
              </div>
            </SelectOption>
          </template>
          <!-- <template #option="{ option }">
            <div class="flex flex-col">
              <span class="font-medium">{{ option.userName || '-' }}</span>
              <span class="font-medium">用户编号：{{ option.userNo }}</span>
              <span class="text-xs text-gray-500">{{
                option.address || '暂无地址'
              }}</span>
            </div>
          </template> -->
        </Select>
      </template>
    </BasicForm>
  </BasicModal>
</template>
