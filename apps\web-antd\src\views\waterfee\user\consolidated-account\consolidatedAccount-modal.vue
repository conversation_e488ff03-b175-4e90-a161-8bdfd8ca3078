<script setup lang="ts">
import type { UserVO } from '#/api/waterfee/user/archivesManage/model.d';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Select, SelectOption } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { listUser } from '#/api/waterfee/user/archivesManage';
import {
  addConsolidatedAccount,
  getConsolidatedAccount,
  updateConsolidatedAccount,
} from '#/api/waterfee/user/consolidatedAccount/index';

import { modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      class: 'w-full',
    },
  },
  schema: modalSchema(),
  showDefaultActions: false,
});

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[800px]',
  fullscreenButton: true,
  closeOnClickModal: false,
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);
    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;
    if (isUpdate.value && id) {
      const record = await getConsolidatedAccount(id);
      await formApi.setValues(record);
    }
    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate.value
      ? updateConsolidatedAccount(data)
      : addConsolidatedAccount(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}

const userOptions = ref<UserVO[]>([]);
const userLoading = ref(false);

async function handleDropdownVisibleChange(open: boolean) {
  if (open && userOptions.value.length === 0) {
    userLoading.value = true;
    try {
      const res = await listUser({
        auditStatus: 'finish',
      });
      if (res && res.rows) {
        userOptions.value = res.rows;
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      userLoading.value = false;
    }
  }
}

// 搜索过滤函数
function filterUserOption(input: string, option: any) {
  const userData = userOptions.value.find(
    (user: UserVO) => user.userNo === option.value,
  );
  if (!userData) return false;

  const searchText = input.toLowerCase();
  return (
    (userData.userName &&
      userData.userName.toLowerCase().includes(searchText)) ||
    (userData.userNo && userData.userNo.toLowerCase().includes(searchText)) ||
    (userData.address && userData.address.toLowerCase().includes(searchText))
  );
}

// 获取已选择用户的名称
function getSelectedUserName(userNo: string) {
  const user = userOptions.value.find((item: UserVO) => item.userNo === userNo);
  return user ? user.userName || '未命名用户' : userNo;
}
</script>

<template>
  <BasicModal :close-on-click-modal="false" :title="title">
    <BasicForm>
      <template #userList="slotProps">
        <Select
          v-model:value="slotProps.formModel.userList"
          mode="multiple"
          placeholder="请选择合收户用户"
          :loading="userLoading"
          :max-tag-count="3"
          :max-tag-text-length="10"
          show-search
          :filter-option="filterUserOption"
          @dropdown-visible-change="handleDropdownVisibleChange"
        >
          <!-- 自定义选项显示 -->
          <template v-for="item in userOptions" :key="item.userNo">
            <SelectOption
              :value="item.userNo"
              :title="`${item.userName} - ${item.userNo}`"
            >
              <div class="flex flex-col py-1">
                <div class="flex items-center justify-between">
                  <span class="font-medium text-gray-900">{{
                    item.userName || '未命名用户'
                  }}</span>
                  <span
                    class="rounded bg-blue-50 px-2 py-0.5 text-xs text-blue-600"
                  >
                    {{ item.userNo }}
                  </span>
                </div>
                <div class="mt-1 flex items-center gap-2">
                  <span class="text-xs text-gray-500">
                    <i class="anticon anticon-environment"></i>
                    {{ item.address || '暂无地址信息' }}
                  </span>
                </div>
                <div v-if="item.phone" class="mt-0.5 flex items-center gap-2">
                  <span class="text-xs text-gray-500">
                    <i class="anticon anticon-phone"></i>
                    {{ item.phone }}
                  </span>
                </div>
              </div>
            </SelectOption>
          </template>

          <!-- 自定义已选择标签显示 -->
          <template #tagRender="{ value, closable, onClose }">
            <div
              class="mb-1 mr-1 inline-flex items-center gap-1 rounded bg-blue-50 px-2 py-1 text-xs text-blue-700"
            >
              <span class="max-w-[100px] truncate">{{
                getSelectedUserName(value)
              }}</span>
              <span class="text-blue-500">({{ value }})</span>
              <span
                v-if="closable"
                class="ml-1 cursor-pointer hover:text-blue-900"
                @click="onClose"
              >
                ×
              </span>
            </div>
          </template>

          <!-- 空状态显示 -->
          <template #notFoundContent>
            <div class="py-4 text-center text-gray-500">
              <i class="anticon anticon-inbox mb-2 block text-2xl"></i>
              <div>暂无用户数据</div>
              <div class="mt-1 text-xs">请检查用户审核状态</div>
            </div>
          </template>
        </Select>
      </template>
    </BasicForm>
  </BasicModal>
</template>
